<template>
    <div class="quiz-player">
        <!-- 题目进度指示器 -->
        <div class="quiz-header">
            <div class="progress-info">
                <span class="current-question">{{ currentQuestionIndex + 1 }}</span>
                <span class="separator">/</span>
                <span class="total-questions">{{ questions.length }}</span>
            </div>
            <div class="progress-bar">
                <div class="progress-fill" :style="{ width: progressPercentage + '%' }"></div>
            </div>
        </div>

        <!-- Swiper 容器 -->
        <div class="quiz-content">
            <Swiper
                ref="swiperRef"
                :modules="[Navigation]"
                :slides-per-view="1"
                :space-between="0"
                :allow-touch-move="false"
                :navigation="false"
                @swiper="onSwiperInit"
                @slide-change="onSlideChange"
                class="quiz-swiper"
            >
                <SwiperSlide v-for="(question, index) in questions" :key="question.id">
                    <div class="question-slide">
                        <!-- 题目标题 -->
                        <div class="question-header">
                            <h2 class="quiz-title">{{ quizTitle }}</h2>
                            <div
                                class="question-type-badge"
                                :class="getQuestionTypeBadgeClass(question.type)"
                            >
                                {{ getQuestionTypeText(question.type) }}
                            </div>
                        </div>

                        <!-- 题目内容 -->
                        <div class="question-content">
                            <div class="question-text">
                                <span class="question-number">{{ index + 1 }}.</span>
                                {{ question.question }}
                            </div>
                        </div>

                        <!-- 选项区域 -->
                        <div class="options-container">
                            <!-- 单选题 -->
                            <template v-if="question.type === 'single'">
                                <div
                                    v-for="option in question.options"
                                    :key="option.key"
                                    class="option-item"
                                    :class="{
                                        selected: userAnswers[question.id] === option.key,
                                        disabled: isAnswered(question.id)
                                    }"
                                    @click="selectSingleOption(question.id, option.key)"
                                >
                                    <div class="option-radio">
                                        <div
                                            class="radio-inner"
                                            v-if="userAnswers[question.id] === option.key"
                                        ></div>
                                    </div>
                                    <div class="option-content">
                                        <span class="option-key">{{ option.key }}.</span>
                                        <span class="option-text">{{ option.text }}</span>
                                    </div>
                                </div>
                            </template>

                            <!-- 多选题 -->
                            <template v-else-if="question.type === 'multi'">
                                <div
                                    v-for="option in question.options"
                                    :key="option.key"
                                    class="option-item"
                                    :class="{
                                        selected: isMultiOptionSelected(question.id, option.key),
                                        disabled: isAnswered(question.id)
                                    }"
                                    @click="toggleMultiOption(question.id, option.key)"
                                >
                                    <div class="option-checkbox">
                                        <i
                                            class="pi pi-check"
                                            v-if="isMultiOptionSelected(question.id, option.key)"
                                        ></i>
                                    </div>
                                    <div class="option-content">
                                        <span class="option-key">{{ option.key }}.</span>
                                        <span class="option-text">{{ option.text }}</span>
                                    </div>
                                </div>
                            </template>

                            <!-- 判断题 -->
                            <template v-else-if="question.type === 'true_false'">
                                <div
                                    class="option-item"
                                    :class="{
                                        selected: userAnswers[question.id] === true,
                                        disabled: isAnswered(question.id)
                                    }"
                                    @click="selectTrueFalseOption(question.id, true)"
                                >
                                    <div class="option-radio">
                                        <div
                                            class="radio-inner"
                                            v-if="userAnswers[question.id] === true"
                                        ></div>
                                    </div>
                                    <div class="option-content">
                                        <span class="option-text">正确</span>
                                    </div>
                                </div>
                                <div
                                    class="option-item"
                                    :class="{
                                        selected: userAnswers[question.id] === false,
                                        disabled: isAnswered(question.id)
                                    }"
                                    @click="selectTrueFalseOption(question.id, false)"
                                >
                                    <div class="option-radio">
                                        <div
                                            class="radio-inner"
                                            v-if="userAnswers[question.id] === false"
                                        ></div>
                                    </div>
                                    <div class="option-content">
                                        <span class="option-text">错误</span>
                                    </div>
                                </div>
                            </template>
                        </div>

                        <!-- 提交按钮 -->
                        <div
                            class="submit-section"
                            v-if="hasUserAnswer(question.id) && !isAnswered(question.id)"
                        >
                            <Button
                                class="submit-btn"
                                @click="submitAnswer(question.id)"
                                :disabled="!hasUserAnswer(question.id)"
                            >
                                提交答案
                            </Button>
                        </div>
                    </div>
                </SwiperSlide>
            </Swiper>
        </div>

        <!-- 错误提示面板 -->
        <ErrorPanel
            v-model:visible="showErrorPanel"
            :question="currentErrorQuestion"
            :user-answer="currentUserAnswer"
            :correct-answer="currentCorrectAnswer"
            @confirm="handleErrorConfirm"
        />
    </div>
</template>

<script setup>
import { ref, computed, watch, nextTick } from 'vue';
import { Swiper, SwiperSlide } from 'swiper/vue';
import { Navigation } from 'swiper/modules';
import ErrorPanel from './ErrorPanel.vue';

// 引入 Swiper 样式
import 'swiper/css';
import 'swiper/css/navigation';

// Props
const props = defineProps({
    quizData: {
        type: Object,
        required: true
    }
});

// Emits
const emit = defineEmits(['quiz-complete']);

// 响应式数据
const swiperRef = ref(null);
const swiperInstance = ref(null);
const currentQuestionIndex = ref(0);
const userAnswers = ref({});
const answeredQuestions = ref(new Set());
const showErrorPanel = ref(false);
const currentErrorQuestion = ref(null);
const currentUserAnswer = ref(null);
const currentCorrectAnswer = ref(null);

// 计算属性
const questions = computed(() => props.quizData?.questions || []);
const quizTitle = computed(() => props.quizData?.quizTitle || '');
const progressPercentage = computed(() => {
    if (questions.value.length === 0) return 0;
    return ((currentQuestionIndex.value + 1) / questions.value.length) * 100;
});

// Swiper 初始化
const onSwiperInit = swiper => {
    swiperInstance.value = swiper;
};

// 滑动变化处理
const onSlideChange = swiper => {
    currentQuestionIndex.value = swiper.activeIndex;
};

// 题目类型相关方法
const getQuestionTypeText = type => {
    const typeMap = {
        single: '单选题',
        multi: '多选题',
        true_false: '判断题'
    };
    return typeMap[type] || '未知题型';
};

const getQuestionTypeBadgeClass = type => {
    const classMap = {
        single: 'badge-single',
        multi: 'badge-multi',
        true_false: 'badge-judge'
    };
    return classMap[type] || '';
};

// 答案选择相关方法
const selectSingleOption = (questionId, optionKey) => {
    if (isAnswered(questionId)) return;
    userAnswers.value[questionId] = optionKey;
};

const toggleMultiOption = (questionId, optionKey) => {
    if (isAnswered(questionId)) return;

    if (!userAnswers.value[questionId]) {
        userAnswers.value[questionId] = [];
    }

    const currentAnswers = userAnswers.value[questionId];
    const index = currentAnswers.indexOf(optionKey);

    if (index > -1) {
        currentAnswers.splice(index, 1);
    } else {
        currentAnswers.push(optionKey);
    }
};

const selectTrueFalseOption = (questionId, value) => {
    if (isAnswered(questionId)) return;
    userAnswers.value[questionId] = value;
};

// 辅助方法
const isAnswered = questionId => {
    return answeredQuestions.value.has(questionId);
};

const hasUserAnswer = questionId => {
    const answer = userAnswers.value[questionId];
    if (Array.isArray(answer)) {
        return answer.length > 0;
    }
    return answer !== undefined && answer !== null;
};

const isMultiOptionSelected = (questionId, optionKey) => {
    const answers = userAnswers.value[questionId];
    return Array.isArray(answers) && answers.includes(optionKey);
};

// 提交答案
const submitAnswer = questionId => {
    const question = questions.value.find(q => q.id === questionId);
    if (!question) return;

    const userAnswer = userAnswers.value[questionId];
    const correctAnswer = question.answer;

    // 标记为已回答
    answeredQuestions.value.add(questionId);

    // 检查答案是否正确
    const isCorrect = checkAnswer(userAnswer, correctAnswer);

    if (isCorrect) {
        // 答案正确，自动进入下一题
        setTimeout(() => {
            goToNextQuestion();
        }, 1000);
    } else {
        // 答案错误，显示错误面板
        currentErrorQuestion.value = question;
        currentUserAnswer.value = userAnswer;
        currentCorrectAnswer.value = correctAnswer;
        showErrorPanel.value = true;
    }
};

// 检查答案是否正确
const checkAnswer = (userAnswer, correctAnswer) => {
    if (Array.isArray(correctAnswer)) {
        // 多选题
        if (!Array.isArray(userAnswer)) return false;
        if (userAnswer.length !== correctAnswer.length) return false;
        return userAnswer.sort().join(',') === correctAnswer.sort().join(',');
    } else {
        // 单选题和判断题
        return userAnswer === correctAnswer;
    }
};

// 进入下一题
const goToNextQuestion = () => {
    if (currentQuestionIndex.value < questions.value.length - 1) {
        swiperInstance.value?.slideNext();
    } else {
        // 所有题目完成，触发完成事件
        emitQuizComplete();
    }
};

// 错误面板确认处理
const handleErrorConfirm = () => {
    showErrorPanel.value = false;
    goToNextQuestion();
};

// 触发测验完成事件
const emitQuizComplete = () => {
    const results = questions.value.map(question => {
        const userAnswer = userAnswers.value[question.id];
        const isCorrect = checkAnswer(userAnswer, question.answer);
        return {
            questionId: question.id,
            question: question.question,
            userAnswer,
            correctAnswer: question.answer,
            isCorrect
        };
    });

    const correctCount = results.filter(r => r.isCorrect).length;
    const accuracy = (correctCount / results.length) * 100;

    emit('quiz-complete', {
        results,
        correctCount,
        totalCount: results.length,
        accuracy: Math.round(accuracy * 100) / 100
    });
};
</script>

<style lang="scss" scoped>
.quiz-player {
    height: 100vh;
    display: flex;
    flex-direction: column;
    background: #f8f9fa;
}

.quiz-header {
    background: white;
    padding: 16px 20px;
    border-bottom: 1px solid #e9ecef;
    position: sticky;
    top: 0;
    z-index: 10;

    .progress-info {
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 12px;
        font-size: 16px;
        font-weight: 600;

        .current-question {
            color: #007bff;
        }

        .separator {
            margin: 0 8px;
            color: #6c757d;
        }

        .total-questions {
            color: #6c757d;
        }
    }

    .progress-bar {
        height: 4px;
        background: #e9ecef;
        border-radius: 2px;
        overflow: hidden;

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #007bff, #0056b3);
            border-radius: 2px;
            transition: width 0.3s ease;
        }
    }
}

.quiz-content {
    flex: 1;
    overflow: hidden;

    .quiz-swiper {
        height: 100%;

        .question-slide {
            height: 100%;
            padding: 20px;
            display: flex;
            flex-direction: column;
            overflow-y: auto;
        }
    }
}

.question-header {
    margin-bottom: 24px;

    .quiz-title {
        font-size: 18px;
        font-weight: 600;
        color: #212529;
        margin-bottom: 12px;
        line-height: 1.4;
    }

    .question-type-badge {
        display: inline-block;
        padding: 4px 12px;
        border-radius: 12px;
        font-size: 12px;
        font-weight: 500;

        &.badge-single {
            background: #e3f2fd;
            color: #1976d2;
        }

        &.badge-multi {
            background: #f3e5f5;
            color: #7b1fa2;
        }

        &.badge-judge {
            background: #e8f5e8;
            color: #388e3c;
        }
    }
}

.question-content {
    margin-bottom: 32px;

    .question-text {
        font-size: 16px;
        line-height: 1.6;
        color: #212529;
        padding: 20px;
        background: white;
        border-radius: 12px;
        border-left: 4px solid #007bff;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

        .question-number {
            font-weight: 600;
            color: #007bff;
            margin-right: 8px;
        }
    }
}

.options-container {
    flex: 1;
    margin-bottom: 24px;

    .option-item {
        display: flex;
        align-items: flex-start;
        padding: 16px 20px;
        margin-bottom: 12px;
        background: white;
        border-radius: 12px;
        border: 2px solid #e9ecef;
        cursor: pointer;
        transition: all 0.2s ease;

        &:hover:not(.disabled) {
            border-color: #007bff;
            box-shadow: 0 2px 8px rgba(0, 123, 255, 0.15);
        }

        &.selected {
            border-color: #007bff;
            background: #f8f9ff;
        }

        &.disabled {
            cursor: not-allowed;
            opacity: 0.6;
        }

        .option-radio {
            width: 20px;
            height: 20px;
            border: 2px solid #dee2e6;
            border-radius: 50%;
            margin-right: 12px;
            margin-top: 2px;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-shrink: 0;

            .radio-inner {
                width: 10px;
                height: 10px;
                background: #007bff;
                border-radius: 50%;
            }
        }

        .option-checkbox {
            width: 20px;
            height: 20px;
            border: 2px solid #dee2e6;
            border-radius: 4px;
            margin-right: 12px;
            margin-top: 2px;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-shrink: 0;
            background: white;

            i {
                color: #007bff;
                font-size: 12px;
            }
        }

        &.selected .option-radio {
            border-color: #007bff;
        }

        &.selected .option-checkbox {
            border-color: #007bff;
            background: #007bff;

            i {
                color: white;
            }
        }

        .option-content {
            flex: 1;
            line-height: 1.5;

            .option-key {
                font-weight: 600;
                color: #007bff;
                margin-right: 8px;
            }

            .option-text {
                color: #212529;
            }
        }
    }
}

.submit-section {
    margin-top: auto;
    padding-top: 20px;

    .submit-btn {
        width: 100%;
        height: 48px;
        font-size: 16px;
        font-weight: 600;
        border-radius: 12px;
        background: linear-gradient(135deg, #007bff, #0056b3);
        border: none;
        color: white;
        transition: all 0.2s ease;

        &:hover:not(:disabled) {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3);
        }

        &:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }
    }
}

// 响应式设计
@media (max-width: 768px) {
    .quiz-header {
        padding: 12px 16px;
    }

    .question-slide {
        padding: 16px;
    }

    .question-content .question-text {
        padding: 16px;
        font-size: 15px;
    }

    .options-container .option-item {
        padding: 14px 16px;
    }
}
</style>
