<template>
    <div class="quiz-result">
        <!-- 结果头部 -->
        <div class="result-header">
            <div class="completion-icon">
                <i class="pi pi-check-circle" v-if="accuracy >= 80"></i>
                <i class="pi pi-exclamation-triangle" v-else-if="accuracy >= 60"></i>
                <i class="pi pi-times-circle" v-else></i>
            </div>
            <h1 class="result-title">测试完成</h1>
            <p class="result-subtitle">{{ getResultMessage() }}</p>
        </div>

        <!-- 成绩统计 -->
        <div class="score-section">
            <div class="score-card">
                <div class="score-circle">
                    <div class="circle-progress" :style="{ '--progress': accuracy }">
                        <div class="circle-inner">
                            <span class="score-number">{{ Math.round(accuracy) }}</span>
                            <span class="score-unit">%</span>
                        </div>
                    </div>
                </div>
                <div class="score-details">
                    <div class="score-item">
                        <span class="score-label">正确题数</span>
                        <span class="score-value correct">{{ correctCount }}</span>
                    </div>
                    <div class="score-item">
                        <span class="score-label">错误题数</span>
                        <span class="score-value incorrect">{{ totalCount - correctCount }}</span>
                    </div>
                    <div class="score-item">
                        <span class="score-label">总题数</span>
                        <span class="score-value total">{{ totalCount }}</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- 题目详情 -->
        <div class="details-section">
            <div class="section-header">
                <h3 class="section-title">答题详情</h3>
                <div class="filter-tabs">
                    <button
                        class="filter-tab"
                        :class="{ active: activeFilter === 'all' }"
                        @click="activeFilter = 'all'"
                    >
                        全部 ({{ results.length }})
                    </button>
                    <button
                        class="filter-tab"
                        :class="{ active: activeFilter === 'correct' }"
                        @click="activeFilter = 'correct'"
                    >
                        正确 ({{ correctCount }})
                    </button>
                    <button
                        class="filter-tab"
                        :class="{ active: activeFilter === 'incorrect' }"
                        @click="activeFilter = 'incorrect'"
                    >
                        错误 ({{ totalCount - correctCount }})
                    </button>
                </div>
            </div>

            <div class="results-list">
                <div
                    v-for="(result, index) in filteredResults"
                    :key="result.questionId"
                    class="result-item"
                    :class="{ correct: result.isCorrect, incorrect: !result.isCorrect }"
                >
                    <div class="result-indicator">
                        <i class="pi pi-check" v-if="result.isCorrect"></i>
                        <i class="pi pi-times" v-else></i>
                    </div>
                    <div class="result-content">
                        <div class="question-text">
                            <span class="question-number">{{ getOriginalIndex(result.questionId) + 1 }}.</span>
                            {{ result.question }}
                        </div>
                        <div class="answer-info">
                            <div class="answer-row">
                                <span class="answer-label">您的答案：</span>
                                <span class="answer-value user-answer" :class="{ incorrect: !result.isCorrect }">
                                    {{ formatAnswer(result.userAnswer) }}
                                </span>
                            </div>
                            <div class="answer-row" v-if="!result.isCorrect">
                                <span class="answer-label">正确答案：</span>
                                <span class="answer-value correct-answer">
                                    {{ formatAnswer(result.correctAnswer) }}
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 操作按钮 -->
        <div class="action-section">
            <Button class="action-btn secondary" @click="$emit('restart')">
                <i class="pi pi-refresh"></i>
                <span>重新测试</span>
            </Button>
            <Button class="action-btn primary" @click="$emit('back-to-home')">
                <i class="pi pi-home"></i>
                <span>返回首页</span>
            </Button>
        </div>
    </div>
</template>

<script setup>
import { ref, computed } from 'vue';

// Props
const props = defineProps({
    results: {
        type: Array,
        required: true
    },
    correctCount: {
        type: Number,
        required: true
    },
    totalCount: {
        type: Number,
        required: true
    },
    accuracy: {
        type: Number,
        required: true
    },
    quizData: {
        type: Object,
        required: true
    }
});

// Emits
const emit = defineEmits(['restart', 'back-to-home']);

// 响应式数据
const activeFilter = ref('all');

// 计算属性
const filteredResults = computed(() => {
    switch (activeFilter.value) {
        case 'correct':
            return props.results.filter(r => r.isCorrect);
        case 'incorrect':
            return props.results.filter(r => !r.isCorrect);
        default:
            return props.results;
    }
});

// 方法
const getResultMessage = () => {
    if (props.accuracy >= 90) {
        return '优秀！您的表现非常出色！';
    } else if (props.accuracy >= 80) {
        return '良好！您掌握得不错！';
    } else if (props.accuracy >= 60) {
        return '及格！还有提升空间！';
    } else {
        return '需要加强！建议多加练习！';
    }
};

const formatAnswer = (answer) => {
    if (answer === null || answer === undefined) {
        return '未选择';
    }
    
    if (typeof answer === 'boolean') {
        return answer ? '正确' : '错误';
    }
    
    if (Array.isArray(answer)) {
        return answer.length > 0 ? answer.join(', ') : '未选择';
    }
    
    return answer;
};

const getOriginalIndex = (questionId) => {
    return props.quizData.questions.findIndex(q => q.id === questionId);
};
</script>

<style lang="scss" scoped>
.quiz-result {
    min-height: 100vh;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    padding: 20px;
    display: flex;
    flex-direction: column;
}

.result-header {
    text-align: center;
    margin-bottom: 32px;
    color: white;

    .completion-icon {
        margin-bottom: 16px;

        i {
            font-size: 64px;
            
            &.pi-check-circle {
                color: #48bb78;
            }
            
            &.pi-exclamation-triangle {
                color: #ed8936;
            }
            
            &.pi-times-circle {
                color: #f56565;
            }
        }
    }

    .result-title {
        font-size: 28px;
        font-weight: 700;
        margin-bottom: 8px;
    }

    .result-subtitle {
        font-size: 16px;
        opacity: 0.9;
    }
}

.score-section {
    margin-bottom: 32px;

    .score-card {
        background: white;
        border-radius: 16px;
        padding: 32px;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        display: flex;
        align-items: center;
        gap: 32px;

        .score-circle {
            flex-shrink: 0;

            .circle-progress {
                width: 120px;
                height: 120px;
                border-radius: 50%;
                background: conic-gradient(
                    #4299e1 0deg,
                    #4299e1 calc(var(--progress) * 3.6deg),
                    #e2e8f0 calc(var(--progress) * 3.6deg),
                    #e2e8f0 360deg
                );
                display: flex;
                align-items: center;
                justify-content: center;
                position: relative;

                .circle-inner {
                    width: 90px;
                    height: 90px;
                    background: white;
                    border-radius: 50%;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    flex-direction: column;

                    .score-number {
                        font-size: 32px;
                        font-weight: 700;
                        color: #2d3748;
                        line-height: 1;
                    }

                    .score-unit {
                        font-size: 14px;
                        color: #718096;
                        margin-top: -4px;
                    }
                }
            }
        }

        .score-details {
            flex: 1;

            .score-item {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 12px 0;
                border-bottom: 1px solid #e2e8f0;

                &:last-child {
                    border-bottom: none;
                }

                .score-label {
                    font-size: 14px;
                    color: #718096;
                }

                .score-value {
                    font-size: 18px;
                    font-weight: 600;

                    &.correct {
                        color: #48bb78;
                    }

                    &.incorrect {
                        color: #f56565;
                    }

                    &.total {
                        color: #4299e1;
                    }
                }
            }
        }
    }
}

.details-section {
    flex: 1;
    background: white;
    border-radius: 16px;
    padding: 24px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    margin-bottom: 24px;

    .section-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
        flex-wrap: wrap;
        gap: 16px;

        .section-title {
            font-size: 18px;
            font-weight: 600;
            color: #2d3748;
        }

        .filter-tabs {
            display: flex;
            gap: 8px;

            .filter-tab {
                padding: 6px 12px;
                border: 1px solid #e2e8f0;
                background: white;
                border-radius: 6px;
                font-size: 12px;
                cursor: pointer;
                transition: all 0.2s ease;

                &:hover {
                    border-color: #4299e1;
                }

                &.active {
                    background: #4299e1;
                    color: white;
                    border-color: #4299e1;
                }
            }
        }
    }

    .results-list {
        max-height: 400px;
        overflow-y: auto;

        .result-item {
            display: flex;
            gap: 16px;
            padding: 16px;
            margin-bottom: 12px;
            border-radius: 8px;
            border: 1px solid #e2e8f0;

            &.correct {
                background: #f0fff4;
                border-color: #9ae6b4;
            }

            &.incorrect {
                background: #fff5f5;
                border-color: #feb2b2;
            }

            .result-indicator {
                flex-shrink: 0;
                width: 24px;
                height: 24px;
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
                margin-top: 2px;

                i {
                    font-size: 14px;
                }
            }

            &.correct .result-indicator {
                background: #48bb78;
                color: white;
            }

            &.incorrect .result-indicator {
                background: #f56565;
                color: white;
            }

            .result-content {
                flex: 1;

                .question-text {
                    font-size: 14px;
                    line-height: 1.5;
                    color: #2d3748;
                    margin-bottom: 8px;

                    .question-number {
                        font-weight: 600;
                        color: #4299e1;
                        margin-right: 6px;
                    }
                }

                .answer-info {
                    .answer-row {
                        display: flex;
                        gap: 8px;
                        margin-bottom: 4px;
                        font-size: 12px;

                        .answer-label {
                            color: #718096;
                            flex-shrink: 0;
                        }

                        .answer-value {
                            font-weight: 500;

                            &.user-answer.incorrect {
                                color: #f56565;
                            }

                            &.correct-answer {
                                color: #48bb78;
                            }
                        }
                    }
                }
            }
        }
    }
}

.action-section {
    display: flex;
    gap: 12px;
    justify-content: center;

    .action-btn {
        display: flex;
        align-items: center;
        gap: 8px;
        padding: 12px 24px;
        border-radius: 8px;
        font-weight: 600;
        font-size: 14px;
        cursor: pointer;
        transition: all 0.2s ease;
        border: none;

        &.secondary {
            background: white;
            color: #4299e1;
            border: 2px solid #4299e1;

            &:hover {
                background: #4299e1;
                color: white;
            }
        }

        &.primary {
            background: linear-gradient(135deg, #4299e1, #3182ce);
            color: white;

            &:hover {
                transform: translateY(-2px);
                box-shadow: 0 4px 12px rgba(66, 153, 225, 0.3);
            }
        }

        i {
            font-size: 14px;
        }
    }
}

// 响应式设计
@media (max-width: 768px) {
    .quiz-result {
        padding: 16px;
    }

    .score-section .score-card {
        flex-direction: column;
        gap: 24px;
        padding: 24px;
    }

    .details-section {
        padding: 16px;

        .section-header {
            flex-direction: column;
            align-items: flex-start;
        }
    }

    .action-section {
        flex-direction: column;

        .action-btn {
            width: 100%;
            justify-content: center;
        }
    }
}
</style>
