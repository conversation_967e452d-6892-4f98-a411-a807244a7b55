<template>
    <div class="result-page">
        <!-- 加载状态 -->
        <div v-if="loading" class="loading-container">
            <div class="loading-spinner"></div>
            <p>正在加载考试结果...</p>
        </div>

        <!-- 结果不存在 -->
        <div v-else-if="!resultData" class="not-found">
            <i class="pi pi-exclamation-triangle"></i>
            <h3>结果不存在</h3>
            <p>未找到考试结果，请重新参加考试</p>
            <button class="back-btn" @click="goToList">
                <i class="pi pi-arrow-left"></i>
                <span>返回列表</span>
            </button>
        </div>

        <!-- 考试结果内容 -->
        <div v-else class="quiz-result">
            <!-- 结果头部 -->
            <div class="result-header">
                <div class="completion-icon">
                    <i class="pi pi-check-circle" v-if="resultData.accuracy >= 80"></i>
                    <i class="pi pi-exclamation-triangle" v-else-if="resultData.accuracy >= 60"></i>
                    <i class="pi pi-times-circle" v-else></i>
                </div>
                <h1 class="result-title">{{ resultData.examData.name }}</h1>
                <h2 class="result-subtitle">测试完成</h2>
                <p class="result-message">{{ getResultMessage() }}</p>
            </div>

            <!-- 成绩统计 -->
            <div class="score-section">
                <div class="score-card">
                    <div class="score-circle">
                        <div class="circle-progress" :style="{ '--progress': resultData.accuracy }">
                            <div class="circle-inner">
                                <span class="score-number">{{
                                    Math.round(resultData.accuracy)
                                }}</span>
                                <span class="score-unit">%</span>
                            </div>
                        </div>
                    </div>
                    <div class="score-details">
                        <div class="score-item">
                            <span class="score-label">正确题数</span>
                            <span class="score-value correct">{{ resultData.correctCount }}</span>
                        </div>
                        <div class="score-item">
                            <span class="score-label">错误题数</span>
                            <span class="score-value incorrect">{{
                                resultData.totalCount - resultData.correctCount
                            }}</span>
                        </div>
                        <div class="score-item">
                            <span class="score-label">总题数</span>
                            <span class="score-value total">{{ resultData.totalCount }}</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 答题详情筛选 -->
            <div class="filter-section">
                <h3 class="filter-title">答题详情</h3>
                <div class="filter-buttons">
                    <button
                        class="filter-btn"
                        :class="{ active: activeFilter === 'all' }"
                        @click="activeFilter = 'all'"
                    >
                        全部 ({{ resultData.results.length }})
                    </button>
                    <button
                        class="filter-btn correct"
                        :class="{ active: activeFilter === 'correct' }"
                        @click="activeFilter = 'correct'"
                    >
                        正确 ({{ correctResults.length }})
                    </button>
                    <button
                        class="filter-btn incorrect"
                        :class="{ active: activeFilter === 'incorrect' }"
                        @click="activeFilter = 'incorrect'"
                    >
                        错误 ({{ incorrectResults.length }})
                    </button>
                </div>
            </div>

            <!-- 答题结果列表 -->
            <div class="results-section">
                <div class="results-list">
                    <div
                        v-for="result in filteredResults"
                        :key="result.questionId"
                        class="result-item"
                        :class="{ correct: result.isCorrect, incorrect: !result.isCorrect }"
                    >
                        <div class="result-indicator">
                            <i class="pi pi-check" v-if="result.isCorrect"></i>
                            <i class="pi pi-times" v-else></i>
                        </div>
                        <div class="result-content">
                            <div class="question-text">
                                <span class="question-number"
                                    >{{ getOriginalIndex(result.questionId) + 1 }}.</span
                                >
                                {{ result.question }}
                            </div>
                            <div class="answer-info">
                                <div class="answer-row">
                                    <span class="answer-label">您的答案：</span>
                                    <span
                                        class="answer-value user-answer"
                                        :class="{ incorrect: !result.isCorrect }"
                                    >
                                        {{ formatAnswer(result.userAnswer) }}
                                    </span>
                                </div>
                                <div class="answer-row" v-if="!result.isCorrect">
                                    <span class="answer-label">正确答案：</span>
                                    <span class="answer-value correct-answer">
                                        {{ formatAnswer(result.correctAnswer) }}
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 操作按钮 -->
            <div class="action-section">
                <button class="action-btn secondary" @click="goToList">
                    <i class="pi pi-list"></i>
                    <span>返回列表</span>
                </button>
                <button class="action-btn primary" @click="restartExam">
                    <i class="pi pi-refresh"></i>
                    <span>重新考试</span>
                </button>
            </div>
        </div>
    </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';

// 路由
const route = useRoute();
const router = useRouter();

// 响应式数据
const loading = ref(true);
const resultData = ref(null);
const activeFilter = ref('all');

// 计算属性
const correctResults = computed(() => {
    return resultData.value?.results.filter(r => r.isCorrect) || [];
});

const incorrectResults = computed(() => {
    return resultData.value?.results.filter(r => !r.isCorrect) || [];
});

const filteredResults = computed(() => {
    if (!resultData.value) {
        return [];
    }

    switch (activeFilter.value) {
        case 'correct':
            return correctResults.value;
        case 'incorrect':
            return incorrectResults.value;
        default:
            return resultData.value.results;
    }
});

onMounted(() => {
    loadResultData();
});

// 方法
const loadResultData = async () => {
    try {
        loading.value = true;

        // 从 sessionStorage 获取结果数据
        const storedResult = sessionStorage.getItem('quiz-result');
        if (storedResult) {
            const data = JSON.parse(storedResult);
            // 验证考试ID是否匹配
            if (data.examId === route.params.examId) {
                resultData.value = data;
            }
        }

        // 模拟加载延迟
        await new Promise(resolve => setTimeout(resolve, 800));
    } catch (error) {
        console.error('加载考试结果失败:', error);
        resultData.value = null;
    } finally {
        loading.value = false;
    }
};

const getResultMessage = () => {
    if (!resultData.value) {
        return '';
    }

    const accuracy = resultData.value.accuracy;
    if (accuracy >= 90) {
        return '优秀！您的表现非常出色！';
    } else if (accuracy >= 80) {
        return '良好！您掌握得不错！';
    } else if (accuracy >= 60) {
        return '及格！还有提升空间！';
    } else {
        return '需要加强！建议多加练习！';
    }
};

const formatAnswer = answer => {
    if (answer === null || answer === undefined) {
        return '未选择';
    }

    if (typeof answer === 'boolean') {
        return answer ? '正确' : '错误';
    }

    if (Array.isArray(answer)) {
        return answer.length > 0 ? answer.join(', ') : '未选择';
    }

    return answer;
};

const getOriginalIndex = questionId => {
    if (!resultData.value?.examData?.questions) {
        return 0;
    }
    return resultData.value.examData.questions.findIndex(q => q.id === questionId);
};

const goToList = () => {
    // 清除结果数据
    sessionStorage.removeItem('quiz-result');
    router.push('/smart-trainer/assess/list');
};

const restartExam = () => {
    if (!resultData.value) {
        return;
    }

    // 清除结果数据
    sessionStorage.removeItem('quiz-result');

    // 跳转到考试详情页
    router.push(`/smart-trainer/assess/detail/${resultData.value.examId}`);
};
</script>

<style lang="scss" scoped>
.result-page {
    min-height: 100vh;
    background: #f8f9fa;
}

.loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100vh;
    color: #6c757d;

    .loading-spinner {
        width: 40px;
        height: 40px;
        border: 4px solid #e9ecef;
        border-top: 4px solid #4299e1;
        border-radius: 50%;
        animation: spin 1s linear infinite;
        margin-bottom: 20px;
    }

    @keyframes spin {
        0% {
            transform: rotate(0deg);
        }
        100% {
            transform: rotate(360deg);
        }
    }
}

.not-found {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100vh;
    color: #6c757d;
    text-align: center;

    i {
        font-size: 64px;
        margin-bottom: 20px;
        color: #ffc107;
    }

    h3 {
        font-size: 24px;
        font-weight: 600;
        color: #212529;
        margin-bottom: 8px;
    }

    p {
        font-size: 14px;
        margin-bottom: 24px;
        line-height: 1.5;
    }

    .back-btn {
        display: flex;
        align-items: center;
        gap: 8px;
        padding: 12px 24px;
        background: #4299e1;
        color: white;
        border: none;
        border-radius: 8px;
        font-size: 14px;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.3s ease;

        &:hover {
            background: #3182ce;
            transform: translateY(-2px);
        }

        i {
            font-size: 16px;
        }
    }
}

.quiz-result {
    padding: 20px;
    max-width: 800px;
    margin: 0 auto;
}

.result-header {
    text-align: center;
    background: white;
    padding: 32px 24px;
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    margin-bottom: 24px;

    .completion-icon {
        margin-bottom: 16px;

        i {
            font-size: 64px;

            &.pi-check-circle {
                color: #28a745;
            }

            &.pi-exclamation-triangle {
                color: #ffc107;
            }

            &.pi-times-circle {
                color: #dc3545;
            }
        }
    }

    .result-title {
        font-size: 20px;
        font-weight: 600;
        color: #212529;
        margin-bottom: 4px;
    }

    .result-subtitle {
        font-size: 24px;
        font-weight: 700;
        color: #212529;
        margin-bottom: 8px;
    }

    .result-message {
        font-size: 16px;
        color: #6c757d;
        line-height: 1.5;
    }
}

.score-section {
    margin-bottom: 24px;

    .score-card {
        background: white;
        padding: 32px 24px;
        border-radius: 16px;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
        display: flex;
        align-items: center;
        gap: 32px;

        .score-circle {
            .circle-progress {
                width: 120px;
                height: 120px;
                border-radius: 50%;
                background: conic-gradient(
                    #4299e1 0deg,
                    #4299e1 calc(var(--progress) * 3.6deg),
                    #e9ecef calc(var(--progress) * 3.6deg),
                    #e9ecef 360deg
                );
                display: flex;
                align-items: center;
                justify-content: center;
                position: relative;

                .circle-inner {
                    width: 90px;
                    height: 90px;
                    background: white;
                    border-radius: 50%;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    flex-direction: column;

                    .score-number {
                        font-size: 28px;
                        font-weight: 700;
                        color: #212529;
                        line-height: 1;
                    }

                    .score-unit {
                        font-size: 14px;
                        color: #6c757d;
                        margin-top: 2px;
                    }
                }
            }
        }

        .score-details {
            flex: 1;
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 24px;

            .score-item {
                text-align: center;

                .score-label {
                    display: block;
                    font-size: 14px;
                    color: #6c757d;
                    margin-bottom: 8px;
                }

                .score-value {
                    display: block;
                    font-size: 24px;
                    font-weight: 700;

                    &.correct {
                        color: #28a745;
                    }

                    &.incorrect {
                        color: #dc3545;
                    }

                    &.total {
                        color: #4299e1;
                    }
                }
            }
        }
    }
}

.filter-section {
    background: white;
    padding: 20px 24px;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    margin-bottom: 16px;

    .filter-title {
        font-size: 18px;
        font-weight: 600;
        color: #212529;
        margin-bottom: 16px;
    }

    .filter-buttons {
        display: flex;
        gap: 12px;

        .filter-btn {
            padding: 8px 16px;
            border: 1px solid #dee2e6;
            border-radius: 20px;
            background: white;
            color: #6c757d;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;

            &:hover {
                border-color: #4299e1;
                color: #4299e1;
            }

            &.active {
                background: #4299e1;
                border-color: #4299e1;
                color: white;
            }

            &.correct.active {
                background: #28a745;
                border-color: #28a745;
            }

            &.incorrect.active {
                background: #dc3545;
                border-color: #dc3545;
            }
        }
    }
}

.results-section {
    margin-bottom: 24px;

    .results-list {
        .result-item {
            display: flex;
            gap: 16px;
            padding: 20px;
            margin-bottom: 12px;
            background: white;
            border-radius: 12px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
            border-left: 4px solid transparent;

            &.correct {
                border-left-color: #28a745;
            }

            &.incorrect {
                border-left-color: #dc3545;
            }

            .result-indicator {
                width: 32px;
                height: 32px;
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
                margin-top: 4px;

                i {
                    font-size: 16px;
                    color: white;
                }
            }

            &.correct .result-indicator {
                background: #28a745;
            }

            &.incorrect .result-indicator {
                background: #dc3545;
            }

            .result-content {
                flex: 1;

                .question-text {
                    font-size: 15px;
                    line-height: 1.5;
                    color: #212529;
                    margin-bottom: 12px;

                    .question-number {
                        font-weight: 600;
                        color: #4299e1;
                        margin-right: 8px;
                    }
                }

                .answer-info {
                    .answer-row {
                        display: flex;
                        align-items: flex-start;
                        gap: 8px;
                        margin-bottom: 6px;

                        &:last-child {
                            margin-bottom: 0;
                        }

                        .answer-label {
                            font-size: 13px;
                            color: #6c757d;
                            font-weight: 500;
                            min-width: 80px;
                        }

                        .answer-value {
                            font-size: 13px;
                            font-weight: 500;

                            &.user-answer.incorrect {
                                color: #dc3545;
                            }

                            &.correct-answer {
                                color: #28a745;
                            }
                        }
                    }
                }
            }
        }
    }
}

.action-section {
    display: flex;
    gap: 16px;
    justify-content: center;
    padding: 24px 0;

    .action-btn {
        display: flex;
        align-items: center;
        gap: 8px;
        padding: 12px 24px;
        border-radius: 8px;
        font-size: 14px;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.3s ease;
        border: none;

        i {
            font-size: 16px;
        }

        &.secondary {
            background: #f8f9fa;
            color: #6c757d;
            border: 1px solid #dee2e6;

            &:hover {
                background: #e9ecef;
                color: #495057;
            }
        }

        &.primary {
            background: linear-gradient(135deg, #4299e1 0%, #3182ce 100%);
            color: white;

            &:hover {
                transform: translateY(-2px);
                box-shadow: 0 8px 25px rgba(66, 153, 225, 0.4);
            }
        }
    }
}

// 响应式设计
@media (max-width: 768px) {
    .quiz-result {
        padding: 16px;
    }

    .result-header {
        padding: 24px 20px;

        .completion-icon i {
            font-size: 48px;
        }

        .result-title {
            font-size: 18px;
        }

        .result-subtitle {
            font-size: 20px;
        }

        .result-message {
            font-size: 14px;
        }
    }

    .score-section .score-card {
        flex-direction: column;
        gap: 24px;
        padding: 24px 20px;

        .score-circle .circle-progress {
            width: 100px;
            height: 100px;

            .circle-inner {
                width: 75px;
                height: 75px;

                .score-number {
                    font-size: 24px;
                }
            }
        }

        .score-details {
            grid-template-columns: repeat(3, 1fr);
            gap: 16px;

            .score-item .score-value {
                font-size: 20px;
            }
        }
    }

    .filter-section {
        padding: 16px 20px;

        .filter-title {
            font-size: 16px;
        }

        .filter-buttons {
            flex-wrap: wrap;
            gap: 8px;

            .filter-btn {
                font-size: 13px;
                padding: 6px 12px;
            }
        }
    }

    .results-section .results-list .result-item {
        padding: 16px;
        gap: 12px;

        .result-indicator {
            width: 28px;
            height: 28px;

            i {
                font-size: 14px;
            }
        }

        .result-content {
            .question-text {
                font-size: 14px;
            }

            .answer-info .answer-row {
                .answer-label {
                    font-size: 12px;
                    min-width: 70px;
                }

                .answer-value {
                    font-size: 12px;
                }
            }
        }
    }

    .action-section {
        flex-direction: column;
        gap: 12px;

        .action-btn {
            width: 100%;
            justify-content: center;
        }
    }
}

@media (max-width: 480px) {
    .score-section .score-card .score-details {
        grid-template-columns: 1fr;
        gap: 12px;
    }

    .filter-section .filter-buttons .filter-btn {
        flex: 1;
        text-align: center;
    }
}
</style>
